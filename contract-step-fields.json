{"stepFields": [{"stepNumber": 1, "stepTitle": "Commercial Information", "fields": [{"name": "projectCode", "label": "Project Code", "type": "text", "required": true, "placeholder": "Enter project code"}, {"name": "groupCompany", "label": "Group", "type": "text", "required": false, "placeholder": "Enter group company"}, {"name": "ghbu", "label": "GHBU", "type": "text", "required": false, "placeholder": "Enter GHBU"}, {"name": "hbu", "label": "HBU", "type": "text", "required": false, "placeholder": "Enter HBU"}, {"name": "sdu", "label": "SDU", "type": "text", "required": false, "placeholder": "Enter SDU"}, {"name": "du", "label": "DU", "type": "text", "required": false, "placeholder": "Enter DU"}, {"name": "projectProfile", "label": "Project Profile", "type": "select", "required": false, "options": [{"value": "development", "label": "Development"}, {"value": "maintenance", "label": "Maintenance"}, {"value": "support", "label": "Support"}, {"value": "consulting", "label": "Consulting"}]}, {"name": "verticalAccount", "label": "Vertical Account Manager", "type": "text", "required": false, "placeholder": "Enter vertical account manager"}, {"name": "customerName", "label": "Customer", "type": "text", "required": true, "placeholder": "Enter customer name"}, {"name": "customerSowNo", "label": "Customer SOW NO", "type": "text", "required": false, "placeholder": "Enter customer SOW number"}, {"name": "customerGeo", "label": "Customer Geo", "type": "select", "required": false, "options": [{"value": "americas", "label": "Americas"}, {"value": "emea", "label": "EMEA"}, {"value": "apac", "label": "APAC"}, {"value": "india", "label": "India"}]}, {"name": "projectName", "label": "Project Name", "type": "text", "required": true, "placeholder": "Enter project name"}, {"name": "projectStartDate", "label": "Project Start Date", "type": "date", "required": true}, {"name": "projectEndDate", "label": "Project End Date", "type": "date", "required": true}, {"name": "projectManager", "label": "Project Manager", "type": "text", "required": true, "placeholder": "Enter project manager name"}, {"name": "srProjectManager", "label": "Sr. Project Manager", "type": "text", "required": false, "placeholder": "Enter senior project manager"}, {"name": "poNumber", "label": "PO Number", "type": "text", "required": false, "placeholder": "Enter PO number"}, {"name": "fmoSpocs", "label": "FMO Spocs", "type": "text", "required": false, "placeholder": "Enter FMO SPOC details"}, {"name": "projectDocuments", "label": "Project Documents", "type": "textarea", "required": false, "placeholder": "Enter project documents details"}, {"name": "projectLocation", "label": "Project Location & Plan", "type": "textarea", "required": false, "placeholder": "Enter project location and plan"}, {"name": "companyCode", "label": "Company Code", "type": "text", "required": false, "placeholder": "Enter company code"}, {"name": "sapProjectType", "label": "SAP Project Type", "type": "select", "required": false, "options": [{"value": "internal", "label": "Internal"}, {"value": "external", "label": "External"}, {"value": "hybrid", "label": "Hybrid"}]}, {"name": "financialYear", "label": "Financial Year", "type": "select", "required": false, "options": [{"value": "2024", "label": "2024"}, {"value": "2025", "label": "2025"}, {"value": "2026", "label": "2026"}]}, {"name": "businessArea", "label": "Business Area", "type": "text", "required": false, "placeholder": "Enter business area"}, {"name": "profitCentre", "label": "Profit Centre", "type": "text", "required": false, "placeholder": "Enter profit centre"}]}, {"stepNumber": 2, "stepTitle": "Delivery Related", "fields": [{"name": "deliveryModel", "label": "Delivery Model", "type": "select", "required": false, "options": [{"value": "onshore", "label": "Onshore"}, {"value": "offshore", "label": "Offshore"}, {"value": "hybrid", "label": "Hybrid"}, {"value": "nearshore", "label": "Nearshore"}]}, {"name": "deliveryLocation", "label": "Delivery Location", "type": "text", "required": false, "placeholder": "Enter delivery location"}, {"name": "teamSize", "label": "Team Size", "type": "number", "required": false, "placeholder": "Enter team size"}, {"name": "technologyStack", "label": "Technology Stack", "type": "textarea", "required": false, "placeholder": "Enter technology stack details"}, {"name": "deliveryManager", "label": "Delivery Manager", "type": "text", "required": false, "placeholder": "Enter delivery manager name"}, {"name": "<PERSON><PERSON><PERSON>", "label": "Architect Name", "type": "text", "required": false, "placeholder": "Enter architect name"}]}, {"stepNumber": 3, "stepTitle": "Other", "fields": [{"name": "contractType", "label": "Contract Type", "type": "select", "required": true, "options": [{"value": "SOW", "label": "Statement of Work (SOW)"}, {"value": "MLA", "label": "Master License Agreement (MLA)"}, {"value": "MSA", "label": "Master Service Agreement (MSA)"}, {"value": "GCA", "label": "General Contract Agreement (GCA)"}, {"value": "NDA", "label": "Non-Disclosure Agreement (NDA)"}, {"value": "DPA", "label": "Data Processing Agreement (DPA)"}, {"value": "SLA", "label": "Service Level Agreement (SLA)"}]}, {"name": "contractStatus", "label": "Contract Status", "type": "select", "required": true, "options": [{"value": "Active", "label": "Active"}, {"value": "Pending", "label": "Pending"}, {"value": "Expired", "label": "Expired"}, {"value": "Terminated", "label": "Terminated"}]}, {"name": "riskLevel", "label": "Risk Level", "type": "select", "required": false, "options": [{"value": "low", "label": "Low"}, {"value": "medium", "label": "Medium"}, {"value": "high", "label": "High"}, {"value": "critical", "label": "Critical"}]}, {"name": "complianceRequirements", "label": "Compliance Requirements", "type": "textarea", "required": false, "placeholder": "Enter compliance requirements"}, {"name": "specialTerms", "label": "Special Terms", "type": "textarea", "required": false, "placeholder": "Enter any special terms or conditions"}, {"name": "notes", "label": "Additional Notes", "type": "textarea", "required": false, "placeholder": "Enter any additional notes"}]}]}
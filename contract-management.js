// Contract Management JavaScript

$(document).ready(function() {
    // Initialize the application
    initializeApp();

    // Event listeners
    setupEventListeners();

    // Load initial data
    loadContractTypes();
    loadContracts();
});

// Application initialization
function initializeApp() {
    console.log('Contract Management System Initialized');

    // Set default view mode
    window.currentViewMode = 'card';
    $('.contracts-container').addClass('card-view');
}

// Setup event listeners
function setupEventListeners() {
    // Add new contract button
    $('#addContractBtn').click(function() {
        openContractModal('add');
    });
    
    // Save contract button
    $('#saveContractBtn, #saveCloseBtn').click(function() {
        saveContract();
    });
    
    // Search functionality
    $('#searchInput').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        filterContracts(searchTerm);
    });
    
    // Contract type selection
    $(document).on('click', '.contract-type-item', function() {
        selectContractType($(this));
    });
    
    // Contract item click
    $(document).on('click', '.contract-item', function() {
        const contractId = $(this).data('contract-id');
        openContractModal('edit', contractId);
    });

    // Table row click
    $(document).on('click', '.contracts-table tbody tr', function() {
        const contractId = $(this).data('contract-id');
        openContractModal('edit', contractId);
    });

    // View mode buttons
    $('.view-mode-buttons .btn').click(function() {
        const viewMode = $(this).data('view');
        switchViewMode(viewMode);

        // Update button states
        $('.view-mode-buttons .btn').removeClass('active').addClass('btn-outline-primary').removeClass('btn-primary-custom');
        $(this).addClass('active').removeClass('btn-outline-primary').addClass('btn-primary-custom');
    });

    // Advanced search and table settings (placeholder functionality)
    $('#advancedSearchBtn').click(function() {
        console.log('Advanced Search clicked');
        // Implement advanced search modal here
    });

    $('#tableSettingsBtn').click(function() {
        console.log('Table Settings clicked');
        // Implement table settings modal here
    });

    // Step navigation
    $('#nextStepBtn').click(function() {
        nextStep();
    });

    $('#prevStepBtn').click(function() {
        prevStep();
    });

    // Step progress click
    $(document).on('click', '.step-item', function() {
        const stepNumber = parseInt($(this).data('step'));
        goToStep(stepNumber);
    });

    // Add field buttons
    $('#addFieldBtn1, #addFieldBtn2, #addFieldBtn3').click(function() {
        const stepNumber = $(this).attr('id').slice(-1);
        openAddFieldModal(stepNumber);
    });

    // Save field button
    $('#saveFieldBtn').click(function() {
        saveCustomField();
    });

    // Field type change
    $('#fieldType').change(function() {
        const fieldType = $(this).val();
        if (fieldType === 'select') {
            $('#optionsContainer').show();
        } else {
            $('#optionsContainer').hide();
        }
    });

    // Delete custom field
    $(document).on('click', '.btn-field-delete', function() {
        $(this).closest('.custom-field-item').remove();
    });
}

// Load contract types from JSON
function loadContractTypes() {
    showLoading();
    
    $.ajax({
        url: "contract-types.json",
        dataType: "json",
        success: function(result) {
            renderContractTypes(result.contractTypes);
            hideLoading();
        },
        error: function() {
            console.error('Failed to load contract types');
            hideLoading();
            showEmptyState('#contractTypeGrid', 'Failed to load contract types');
        }
    });
}

// Load contracts from JSON
function loadContracts() {
    showLoading();
    
    $.ajax({
        url: "contracts.json",
        dataType: "json",
        success: function(result) {
            window.contractsData = result.contracts;
            window.currentFilteredContracts = result.contracts;
            renderContracts(result.contracts);
            hideLoading();
        },
        error: function() {
            console.error('Failed to load contracts');
            hideLoading();
            showEmptyState('#contractsContainer', 'Failed to load contracts');
        }
    });
}

// Render contract types
function renderContractTypes(contractTypes) {
    const container = $('#contractTypeGrid');
    container.empty();
    
    contractTypes.forEach(function(type) {
        const typeHtml = `
            <div class="contract-type-item" data-type="${type.code}">
                <div class="contract-type-icon">
                    <i class="${type.icon}"></i>
                </div>
                <div class="contract-type-name">${type.name}</div>
                <div class="contract-type-count">${type.count} contracts</div>
            </div>
        `;
        container.append(typeHtml);
    });
}

// Render contracts
function renderContracts(contracts) {
    const container = $('#contractsContainer');

    if (contracts.length === 0) {
        showEmptyState('#contractsContainer', 'No contracts found');
        return;
    }

    // Render based on current view mode
    if (window.currentViewMode === 'table') {
        renderTableView(contracts, container);
    } else {
        renderCardBasedView(contracts, container);
    }
}

// Render card-based views (card, list, compact)
function renderCardBasedView(contracts, container) {
    container.empty();

    contracts.forEach(function(contract) {
        const contractHtml = `
            <div class="contract-item" data-contract-id="${contract.id}">
                <div class="contract-header">
                    <div>
                        <div class="contract-title">${contract.customerName}</div>
                        <div class="contract-type-badge">${contract.contractType}</div>
                    </div>
                    <div class="status-badge status-${contract.status.toLowerCase()}">
                        ${contract.status}
                    </div>
                </div>
                <div class="contract-details">
                    <div class="contract-detail-item">
                        <span class="contract-detail-label">Project:</span>
                        <span class="contract-detail-value">${contract.projectCode || 'N/A'}</span>
                    </div>
                    <div class="contract-detail-item">
                        <span class="contract-detail-label">Start Date:</span>
                        <span class="contract-detail-value">${formatDate(contract.startDate)}</span>
                    </div>
                    <div class="contract-detail-item">
                        <span class="contract-detail-label">End Date:</span>
                        <span class="contract-detail-value">${formatDate(contract.endDate)}</span>
                    </div>
                    <div class="contract-detail-item">
                        <span class="contract-detail-label">HCL Contact:</span>
                        <span class="contract-detail-value">${contract.hclContactPersons?.[0]?.name || 'N/A'}</span>
                    </div>
                </div>
            </div>
        `;
        container.append(contractHtml);
    });
}

// Render table view
function renderTableView(contracts, container) {
    const tableHtml = `
        <table class="contracts-table">
            <thead>
                <tr>
                    <th>Customer Name</th>
                    <th>Project</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                    <th>HCL Contact</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                ${contracts.map(contract => `
                    <tr data-contract-id="${contract.id}">
                        <td>
                            <div>
                                <strong>${contract.customerName}</strong>
                                <div class="contract-type-badge mt-1">${contract.contractType}</div>
                            </div>
                        </td>
                        <td>${contract.projectCode || 'N/A'}</td>
                        <td>${formatDate(contract.startDate)}</td>
                        <td>${formatDate(contract.endDate)}</td>
                        <td>${contract.hclContactPersons?.[0]?.name || 'N/A'}</td>
                        <td>
                            <span class="status-badge status-${contract.status.toLowerCase()}">
                                ${contract.status}
                            </span>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
    container.html(tableHtml);
}

// Switch view mode
function switchViewMode(viewMode) {
    window.currentViewMode = viewMode;
    const container = $('.contracts-container');

    // Remove all view classes
    container.removeClass('card-view list-view compact-view table-view');

    // Add the appropriate view class
    container.addClass(viewMode + '-view');

    // Re-render contracts with current filtered data or all data
    const dataToRender = window.currentFilteredContracts || window.contractsData;
    if (dataToRender) {
        renderContracts(dataToRender);
    }
}

// Select contract type
function selectContractType(element) {
    $('.contract-type-item').removeClass('active');
    element.addClass('active');
    
    const selectedType = element.data('type');
    filterContractsByType(selectedType);
}

// Filter contracts by type
function filterContractsByType(type) {
    if (!window.contractsData) return;

    let filteredContracts = window.contractsData;

    if (type && type !== 'all') {
        filteredContracts = window.contractsData.filter(contract =>
            contract.contractType.toLowerCase() === type.toLowerCase()
        );
    }

    // Store filtered data for current view
    window.currentFilteredContracts = filteredContracts;
    renderContracts(filteredContracts);
}

// Filter contracts by search term
function filterContracts(searchTerm) {
    if (!window.contractsData) return;

    let filteredContracts = window.contractsData;

    if (searchTerm) {
        filteredContracts = window.contractsData.filter(contract =>
            contract.customerName.toLowerCase().includes(searchTerm) ||
            contract.contractType.toLowerCase().includes(searchTerm) ||
            (contract.projectCode && contract.projectCode.toLowerCase().includes(searchTerm)) ||
            (contract.hclContactPersons?.[0]?.name && contract.hclContactPersons[0].name.toLowerCase().includes(searchTerm))
        );
    }

    // Store filtered data for current view
    window.currentFilteredContracts = filteredContracts;
    renderContracts(filteredContracts);
}

// Open contract modal
function openContractModal(mode, contractId = null) {
    const modal = new bootstrap.Modal(document.getElementById('contractModal'));

    if (mode === 'add') {
        $('#contractModalLabel').html('<i class="bi bi-plus-circle me-2"></i>Add Contract');
        initializeStepModal();
        loadStepFields();
    } else if (mode === 'edit' && contractId) {
        $('#contractModalLabel').html('<i class="bi bi-pencil-square me-2"></i>Edit Contract');
        initializeStepModal();
        loadStepFields(contractId);
    }

    modal.show();
}

// Initialize step modal
function initializeStepModal() {
    // Reset to first step
    window.currentStep = 1;
    window.totalSteps = 3;

    // Reset step indicators
    $('.step-item').removeClass('active completed');
    $('.step-item[data-step="1"]').addClass('active');

    // Reset step content
    $('.step-content').removeClass('active');
    $('#step1').addClass('active');

    // Reset navigation buttons
    updateNavigationButtons();
}

// Load step fields
function loadStepFields(contractId = null) {
    $.ajax({
        url: "contract-step-fields.json",
        dataType: "json",
        success: function(result) {
            renderStepFields(result.stepFields, contractId);
        },
        error: function() {
            console.error('Failed to load step fields');
            showErrorMessage('Failed to load form fields');
        }
    });
}

// Render step fields
function renderStepFields(stepFields, contractId = null) {
    let contractData = null;
    if (contractId && window.contractsData) {
        contractData = window.contractsData.find(c => c.id == contractId);
    }

    stepFields.forEach(function(step) {
        const stepContainer = $(`#step${step.stepNumber}Fields`);
        stepContainer.empty();

        step.fields.forEach(function(field) {
            const fieldHtml = renderStepField(field, contractData);
            stepContainer.append(fieldHtml);
        });
    });
}

// Render individual step field
function renderStepField(field, contractData) {
    const value = contractData ? getNestedValue(contractData, field.name) : '';

    let fieldHtml = '';

    if (field.type === 'select') {
        const options = field.options ? field.options.map(opt =>
            `<option value="${opt.value}" ${value === opt.value ? 'selected' : ''}>${opt.label}</option>`
        ).join('') : '';

        fieldHtml = `
            <div class="custom-field-item">
                <div class="custom-field-header">
                    <label class="custom-field-label">${field.label}${field.required ? ' *' : ''}</label>
                    <div class="custom-field-actions">
                        <button type="button" class="btn-field-action" title="Edit">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn-field-action delete btn-field-delete" title="Delete">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                <select class="form-select" name="${field.name}" ${field.required ? 'required' : ''}>
                    <option value="">Select ${field.label}</option>
                    ${options}
                </select>
            </div>
        `;
    } else if (field.type === 'textarea') {
        fieldHtml = `
            <div class="custom-field-item">
                <div class="custom-field-header">
                    <label class="custom-field-label">${field.label}${field.required ? ' *' : ''}</label>
                    <div class="custom-field-actions">
                        <button type="button" class="btn-field-action" title="Edit">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn-field-action delete btn-field-delete" title="Delete">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                <textarea class="form-control" name="${field.name}" rows="3" ${field.required ? 'required' : ''} placeholder="${field.placeholder || ''}">${value}</textarea>
            </div>
        `;
    } else {
        fieldHtml = `
            <div class="custom-field-item">
                <div class="custom-field-header">
                    <label class="custom-field-label">${field.label}${field.required ? ' *' : ''}</label>
                    <div class="custom-field-actions">
                        <button type="button" class="btn-field-action" title="Edit">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn-field-action delete btn-field-delete" title="Delete">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                <input type="${field.type}" class="form-control" name="${field.name}" value="${value}" ${field.required ? 'required' : ''} placeholder="${field.placeholder || ''}">
            </div>
        `;
    }

    return fieldHtml;
}

// Step navigation functions
function nextStep() {
    if (window.currentStep < window.totalSteps) {
        // Mark current step as completed
        $(`.step-item[data-step="${window.currentStep}"]`).removeClass('active').addClass('completed');

        // Move to next step
        window.currentStep++;
        $(`.step-item[data-step="${window.currentStep}"]`).addClass('active');

        // Show next step content
        $('.step-content').removeClass('active');
        $(`#step${window.currentStep}`).addClass('active');

        updateNavigationButtons();
    }
}

function prevStep() {
    if (window.currentStep > 1) {
        // Remove active from current step
        $(`.step-item[data-step="${window.currentStep}"]`).removeClass('active');

        // Move to previous step
        window.currentStep--;
        $(`.step-item[data-step="${window.currentStep}"]`).removeClass('completed').addClass('active');

        // Show previous step content
        $('.step-content').removeClass('active');
        $(`#step${window.currentStep}`).addClass('active');

        updateNavigationButtons();
    }
}

function goToStep(stepNumber) {
    if (stepNumber >= 1 && stepNumber <= window.totalSteps) {
        // Update step indicators
        $('.step-item').removeClass('active completed');
        for (let i = 1; i < stepNumber; i++) {
            $(`.step-item[data-step="${i}"]`).addClass('completed');
        }
        $(`.step-item[data-step="${stepNumber}"]`).addClass('active');

        // Update current step
        window.currentStep = stepNumber;

        // Show step content
        $('.step-content').removeClass('active');
        $(`#step${stepNumber}`).addClass('active');

        updateNavigationButtons();
    }
}

function updateNavigationButtons() {
    const prevBtn = $('#prevStepBtn');
    const nextBtn = $('#nextStepBtn');
    const saveCloseBtn = $('#saveCloseBtn');

    // Previous button
    if (window.currentStep === 1) {
        prevBtn.prop('disabled', true);
    } else {
        prevBtn.prop('disabled', false);
    }

    // Next/Save button
    if (window.currentStep === window.totalSteps) {
        nextBtn.hide();
        saveCloseBtn.show();
    } else {
        nextBtn.show();
        saveCloseBtn.hide();
    }
}

// Add field modal functions
function openAddFieldModal(stepNumber) {
    window.currentStepForField = stepNumber;

    // Reset form
    $('#addFieldForm')[0].reset();
    $('#optionsContainer').hide();

    // Open modal
    const modal = new bootstrap.Modal(document.getElementById('addFieldModal'));
    modal.show();
}

function saveCustomField() {
    const form = $('#addFieldForm');
    const formData = new FormData(form[0]);

    const fieldData = {
        label: formData.get('fieldLabel'),
        type: formData.get('fieldType'),
        required: formData.get('fieldRequired') === 'on',
        name: generateFieldName(formData.get('fieldLabel')),
        placeholder: `Enter ${formData.get('fieldLabel')}`
    };

    // Add options for select fields
    if (fieldData.type === 'select') {
        const optionsText = $('#fieldOptions').val();
        if (optionsText) {
            fieldData.options = optionsText.split('\n').map(opt => ({
                value: opt.trim().toLowerCase().replace(/\s+/g, '_'),
                label: opt.trim()
            }));
        }
    }

    // Validate required fields
    if (!fieldData.label || !fieldData.type) {
        showErrorMessage('Please fill in all required fields');
        return;
    }

    // Add field to current step
    const stepContainer = $(`#step${window.currentStepForField}Fields`);
    const fieldHtml = renderStepField(fieldData, null);
    stepContainer.append(fieldHtml);

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('addFieldModal'));
    modal.hide();

    showSuccessMessage('Custom field added successfully!');
}

function generateFieldName(label) {
    return label.toLowerCase().replace(/[^a-z0-9]/g, '_').replace(/_+/g, '_').replace(/^_|_$/g, '');
}

// Load contract form
function loadContractForm(contractId = null) {
    $.ajax({
        url: "contract-form-fields.json",
        dataType: "json",
        success: function(result) {
            renderContractForm(result.formFields, contractId);
        },
        error: function() {
            console.error('Failed to load form fields');
            $('#contractForm').html('<div class="alert alert-danger">Failed to load form fields</div>');
        }
    });
}

// Render contract form
function renderContractForm(formFields, contractId = null) {
    const form = $('#contractForm');
    form.empty();
    
    let contractData = null;
    if (contractId && window.contractsData) {
        contractData = window.contractsData.find(c => c.id == contractId);
    }
    
    formFields.forEach(function(section) {
        const sectionHtml = `
            <div class="form-section">
                <h6 class="form-section-title">${section.sectionTitle}</h6>
                <div class="row">
                    ${section.fields.map(field => renderFormField(field, contractData)).join('')}
                </div>
            </div>
        `;
        form.append(sectionHtml);
    });
}

// Render individual form field
function renderFormField(field, contractData) {
    const value = contractData ? getNestedValue(contractData, field.name) : '';
    const colClass = field.colSize || 'col-md-6';
    
    let fieldHtml = '';
    
    if (field.type === 'select') {
        const options = field.options.map(opt => 
            `<option value="${opt.value}" ${value === opt.value ? 'selected' : ''}>${opt.label}</option>`
        ).join('');
        
        fieldHtml = `
            <div class="${colClass} mb-3">
                <label for="${field.name}" class="form-label">${field.label}</label>
                <select class="form-select" id="${field.name}" name="${field.name}" ${field.required ? 'required' : ''}>
                    <option value="">Select ${field.label}</option>
                    ${options}
                </select>
            </div>
        `;
    } else if (field.type === 'textarea') {
        fieldHtml = `
            <div class="${colClass} mb-3">
                <label for="${field.name}" class="form-label">${field.label}</label>
                <textarea class="form-control" id="${field.name}" name="${field.name}" rows="3" ${field.required ? 'required' : ''}>${value}</textarea>
            </div>
        `;
    } else {
        fieldHtml = `
            <div class="${colClass} mb-3">
                <label for="${field.name}" class="form-label">${field.label}</label>
                <input type="${field.type}" class="form-control" id="${field.name}" name="${field.name}" value="${value}" ${field.required ? 'required' : ''}>
            </div>
        `;
    }
    
    return fieldHtml;
}

// Save contract
function saveContract() {
    const formData = new FormData(document.getElementById('contractForm'));
    const contractData = {};

    for (let [key, value] of formData.entries()) {
        contractData[key] = value;
    }

    // Add timestamp and ID for new contracts
    if (!contractData.id) {
        contractData.id = Date.now();
        contractData.createdAt = new Date().toISOString();
    }
    contractData.updatedAt = new Date().toISOString();

    console.log('Saving contract:', contractData);

    // Here you would typically send the data to your backend
    // For now, we'll just show a success message
    showSuccessMessage('Contract saved successfully!');

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('contractModal'));
    modal.hide();

    // Reload contracts
    loadContracts();
}

// Utility functions
function showLoading() {
    $('#loadingOverlay').addClass('show');
}

function hideLoading() {
    $('#loadingOverlay').removeClass('show');
}

function showEmptyState(container, message) {
    const emptyStateHtml = `
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="bi bi-inbox"></i>
            </div>
            <div class="empty-state-title">No Data Available</div>
            <div class="empty-state-description">${message}</div>
        </div>
    `;
    $(container).html(emptyStateHtml);
}

function showSuccessMessage(message) {
    // You can implement a toast notification here
    alert(message);
}

function showErrorMessage(message) {
    // You can implement a toast notification here
    alert('Error: ' + message);
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString();
}

function getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current && current[key], obj) || '';
}

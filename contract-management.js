// Contract Management JavaScript

$(document).ready(function() {
    // Initialize the application
    initializeApp();
    
    // Event listeners
    setupEventListeners();
    
    // Load initial data
    loadContractTypes();
    loadContracts();
});

// Application initialization
function initializeApp() {
    console.log('Contract Management System Initialized');
}

// Setup event listeners
function setupEventListeners() {
    // Add new contract button
    $('#addContractBtn').click(function() {
        openContractModal('add');
    });
    
    // Save contract button
    $('#saveContractBtn').click(function() {
        saveContract();
    });
    
    // Search functionality
    $('#searchInput').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        filterContracts(searchTerm);
    });
    
    // Contract type selection
    $(document).on('click', '.contract-type-item', function() {
        selectContractType($(this));
    });
    
    // Contract item click
    $(document).on('click', '.contract-item', function() {
        const contractId = $(this).data('contract-id');
        openContractModal('edit', contractId);
    });
}

// Load contract types from JSON
function loadContractTypes() {
    showLoading();
    
    $.ajax({
        url: "contract-types.json",
        dataType: "json",
        success: function(result) {
            renderContractTypes(result.contractTypes);
            hideLoading();
        },
        error: function() {
            console.error('Failed to load contract types');
            hideLoading();
            showEmptyState('#contractTypeGrid', 'Failed to load contract types');
        }
    });
}

// Load contracts from JSON
function loadContracts() {
    showLoading();
    
    $.ajax({
        url: "contracts.json",
        dataType: "json",
        success: function(result) {
            window.contractsData = result.contracts;
            renderContracts(result.contracts);
            hideLoading();
        },
        error: function() {
            console.error('Failed to load contracts');
            hideLoading();
            showEmptyState('#contractsContainer', 'Failed to load contracts');
        }
    });
}

// Render contract types
function renderContractTypes(contractTypes) {
    const container = $('#contractTypeGrid');
    container.empty();
    
    contractTypes.forEach(function(type) {
        const typeHtml = `
            <div class="contract-type-item" data-type="${type.code}">
                <div class="contract-type-icon">
                    <i class="${type.icon}"></i>
                </div>
                <div class="contract-type-name">${type.name}</div>
                <div class="contract-type-count">${type.count} contracts</div>
            </div>
        `;
        container.append(typeHtml);
    });
}

// Render contracts
function renderContracts(contracts) {
    const container = $('#contractsContainer');
    container.empty();
    
    if (contracts.length === 0) {
        showEmptyState('#contractsContainer', 'No contracts found');
        return;
    }
    
    contracts.forEach(function(contract) {
        const contractHtml = `
            <div class="contract-item" data-contract-id="${contract.id}">
                <div class="contract-header">
                    <div>
                        <div class="contract-title">${contract.customerName}</div>
                        <div class="contract-type-badge">${contract.contractType}</div>
                    </div>
                    <div class="status-badge status-${contract.status.toLowerCase()}">
                        ${contract.status}
                    </div>
                </div>
                <div class="contract-details">
                    <div class="contract-detail-item">
                        <span class="contract-detail-label">Project:</span>
                        <span class="contract-detail-value">${contract.projectCode || 'N/A'}</span>
                    </div>
                    <div class="contract-detail-item">
                        <span class="contract-detail-label">Start Date:</span>
                        <span class="contract-detail-value">${formatDate(contract.startDate)}</span>
                    </div>
                    <div class="contract-detail-item">
                        <span class="contract-detail-label">End Date:</span>
                        <span class="contract-detail-value">${formatDate(contract.endDate)}</span>
                    </div>
                    <div class="contract-detail-item">
                        <span class="contract-detail-label">HCL Contact:</span>
                        <span class="contract-detail-value">${contract.hclContactPerson?.name || 'N/A'}</span>
                    </div>
                </div>
            </div>
        `;
        container.append(contractHtml);
    });
}

// Select contract type
function selectContractType(element) {
    $('.contract-type-item').removeClass('active');
    element.addClass('active');
    
    const selectedType = element.data('type');
    filterContractsByType(selectedType);
}

// Filter contracts by type
function filterContractsByType(type) {
    if (!window.contractsData) return;
    
    let filteredContracts = window.contractsData;
    
    if (type && type !== 'all') {
        filteredContracts = window.contractsData.filter(contract => 
            contract.contractType.toLowerCase() === type.toLowerCase()
        );
    }
    
    renderContracts(filteredContracts);
}

// Filter contracts by search term
function filterContracts(searchTerm) {
    if (!window.contractsData) return;
    
    let filteredContracts = window.contractsData;
    
    if (searchTerm) {
        filteredContracts = window.contractsData.filter(contract => 
            contract.customerName.toLowerCase().includes(searchTerm) ||
            contract.contractType.toLowerCase().includes(searchTerm) ||
            (contract.projectCode && contract.projectCode.toLowerCase().includes(searchTerm)) ||
            (contract.hclContactPerson?.name && contract.hclContactPerson.name.toLowerCase().includes(searchTerm))
        );
    }
    
    renderContracts(filteredContracts);
}

// Open contract modal
function openContractModal(mode, contractId = null) {
    const modal = new bootstrap.Modal(document.getElementById('contractModal'));
    
    if (mode === 'add') {
        $('#contractModalLabel').html('<i class="bi bi-plus-circle me-2"></i>Add New Contract');
        loadContractForm();
    } else if (mode === 'edit' && contractId) {
        $('#contractModalLabel').html('<i class="bi bi-pencil-square me-2"></i>Edit Contract');
        loadContractForm(contractId);
    }
    
    modal.show();
}

// Load contract form
function loadContractForm(contractId = null) {
    $.ajax({
        url: "contract-form-fields.json",
        dataType: "json",
        success: function(result) {
            renderContractForm(result.formFields, contractId);
        },
        error: function() {
            console.error('Failed to load form fields');
            $('#contractForm').html('<div class="alert alert-danger">Failed to load form fields</div>');
        }
    });
}

// Render contract form
function renderContractForm(formFields, contractId = null) {
    const form = $('#contractForm');
    form.empty();
    
    let contractData = null;
    if (contractId && window.contractsData) {
        contractData = window.contractsData.find(c => c.id == contractId);
    }
    
    formFields.forEach(function(section) {
        const sectionHtml = `
            <div class="form-section">
                <h6 class="form-section-title">${section.sectionTitle}</h6>
                <div class="row">
                    ${section.fields.map(field => renderFormField(field, contractData)).join('')}
                </div>
            </div>
        `;
        form.append(sectionHtml);
    });
}

// Render individual form field
function renderFormField(field, contractData) {
    const value = contractData ? getNestedValue(contractData, field.name) : '';
    const colClass = field.colSize || 'col-md-6';
    
    let fieldHtml = '';
    
    if (field.type === 'select') {
        const options = field.options.map(opt => 
            `<option value="${opt.value}" ${value === opt.value ? 'selected' : ''}>${opt.label}</option>`
        ).join('');
        
        fieldHtml = `
            <div class="${colClass} mb-3">
                <label for="${field.name}" class="form-label">${field.label}</label>
                <select class="form-select" id="${field.name}" name="${field.name}" ${field.required ? 'required' : ''}>
                    <option value="">Select ${field.label}</option>
                    ${options}
                </select>
            </div>
        `;
    } else if (field.type === 'textarea') {
        fieldHtml = `
            <div class="${colClass} mb-3">
                <label for="${field.name}" class="form-label">${field.label}</label>
                <textarea class="form-control" id="${field.name}" name="${field.name}" rows="3" ${field.required ? 'required' : ''}>${value}</textarea>
            </div>
        `;
    } else {
        fieldHtml = `
            <div class="${colClass} mb-3">
                <label for="${field.name}" class="form-label">${field.label}</label>
                <input type="${field.type}" class="form-control" id="${field.name}" name="${field.name}" value="${value}" ${field.required ? 'required' : ''}>
            </div>
        `;
    }
    
    return fieldHtml;
}

// Save contract
function saveContract() {
    const formData = new FormData(document.getElementById('contractForm'));
    const contractData = {};
    
    for (let [key, value] of formData.entries()) {
        contractData[key] = value;
    }
    
    console.log('Saving contract:', contractData);
    
    // Here you would typically send the data to your backend
    // For now, we'll just show a success message
    showSuccessMessage('Contract saved successfully!');
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('contractModal'));
    modal.hide();
    
    // Reload contracts
    loadContracts();
}

// Utility functions
function showLoading() {
    $('#loadingOverlay').addClass('show');
}

function hideLoading() {
    $('#loadingOverlay').removeClass('show');
}

function showEmptyState(container, message) {
    const emptyStateHtml = `
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="bi bi-inbox"></i>
            </div>
            <div class="empty-state-title">No Data Available</div>
            <div class="empty-state-description">${message}</div>
        </div>
    `;
    $(container).html(emptyStateHtml);
}

function showSuccessMessage(message) {
    // You can implement a toast notification here
    alert(message);
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString();
}

function getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current && current[key], obj) || '';
}

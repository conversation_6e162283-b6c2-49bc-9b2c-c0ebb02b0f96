{"contractTypes": [{"code": "SOW", "name": "Statement of Work", "icon": "bi bi-file-earmark-text", "count": 15, "description": "Detailed project scope and deliverables", "subtypes": ["Fixed Price SOW", "Time & Material SOW", "Milestone Based SOW", "Hybrid SOW", "Agile SOW", "Maintenance SOW", "Support SOW", "Development SOW", "Testing SOW", "Implementation SOW", "Consulting SOW", "Training SOW"]}, {"code": "MLA", "name": "Master License Agreement", "icon": "bi bi-shield-check", "count": 8, "description": "Software licensing terms and conditions", "subtypes": ["Software License MLA", "Cloud Service MLA", "Enterprise MLA", "OEM MLA", "Reseller MLA"]}, {"code": "MSA", "name": "Master Service Agreement", "icon": "bi bi-handshake", "count": 12, "description": "Framework for ongoing service relationships", "subtypes": ["IT Services MSA", "Consulting MSA", "Outsourcing MSA", "Professional Services MSA", "Managed Services MSA"]}, {"code": "GCA", "name": "General Contract Agreement", "icon": "bi bi-file-earmark-ruled", "count": 6, "description": "General terms and conditions", "subtypes": ["Service GCA", "Product GCA", "Partnership GCA", "Vendor GCA"]}, {"code": "NDA", "name": "Non-Disclosure Agreement", "icon": "bi bi-lock", "count": 25, "description": "Confidentiality and information protection", "subtypes": ["Mutual NDA", "Unilateral NDA", "Employee NDA", "Vendor NDA", "Partner NDA"]}, {"code": "DPA", "name": "Data Processing Agreement", "icon": "bi bi-database-lock", "count": 10, "description": "Data protection and privacy compliance", "subtypes": ["GDPR DPA", "CCPA DPA", "Cloud DPA", "Vendor DPA"]}, {"code": "SLA", "name": "Service Level Agreement", "icon": "bi bi-speedometer2", "count": 18, "description": "Service performance standards and metrics", "subtypes": ["IT Support SLA", "Cloud Service SLA", "Application SLA", "Infrastructure SLA", "Help Desk SLA"]}]}
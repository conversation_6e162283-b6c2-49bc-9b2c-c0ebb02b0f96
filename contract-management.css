/* Contract Management Styles */

/* Base Styles */
body {
    font-family: var(--font-family-base);
    background-color: var(--background-primary);
    color: var(--text-primary);
    line-height: var(--line-height-base);
}

/* Header Section */
.header-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-teal-dark) 100%);
    color: var(--text-white);
    padding: var(--space-lg) 0;
    margin-bottom: var(--space-xl);
    box-shadow: var(--shadow-md);
}

.page-title {
    font-size: var(--font-size-xxxl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-xs);
}

.page-subtitle {
    font-size: var(--font-size-1_15-rem);
    opacity: 0.9;
    margin-bottom: 0;
}

/* Custom Button Styles */
.btn-primary-custom {
    background: var(--gradient-submit);
    border: none;
    color: var(--text-white);
    font-weight: var(--font-weight-medium);
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--radius-md);
    transition: all var(--transition-duration-base) var(--transition-easing-ease);
    box-shadow: var(--shadow-btn);
}

.btn-primary-custom:hover {
    background: var(--gradient-submit-hover);
    transform: var(--transform-btn-hover);
    box-shadow: var(--shadow-lg);
    color: var(--text-white);
}

/* Main Content */
.main-content {
    padding: 0 var(--space-md);
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--radius-card);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--space-lg);
    background-color: var(--background-secondary);
}

.card-header {
    background-color: var(--background-secondary);
    border-bottom: var(--border-width-sm) solid var(--border-color);
    padding: var(--space-lg);
    border-radius: var(--radius-card) var(--radius-card) 0 0;
}

.card-title {
    color: var(--text-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-1_22-rem);
}

.card-body {
    padding: var(--space-lg);
}

/* Contract Type Grid */
.contract-type-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-md);
    margin-top: var(--space-md);
}

.contract-type-item {
    background: var(--background-secondary);
    border: var(--border-width-sm) solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-duration-base) var(--transition-easing-ease);
    position: relative;
    overflow: hidden;
}

.contract-type-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.contract-type-item.active {
    border-color: var(--primary-color);
    background: var(--background-accent-light);
    box-shadow: var(--shadow-card-selected);
}

.contract-type-icon {
    font-size: var(--font-size-xxxl);
    color: var(--primary-color);
    margin-bottom: var(--space-sm);
}

.contract-type-name {
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-xs);
}

.contract-type-count {
    font-size: var(--font-size-0_9-rem);
    color: var(--text-secondary);
}

/* View Toggle Section */
.view-toggle-section {
    display: flex;
    justify-content: center;
    align-items: center;
}

.view-mode-buttons .btn-group .btn {
    font-size: var(--font-size-0_8-rem);
    padding: var(--space-xxs) var(--space-sm);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transition: all var(--transition-duration-base) var(--transition-easing-ease);
}

.view-mode-buttons .btn-group .btn.active,
.view-mode-buttons .btn-group .btn:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-white);
}

.view-mode-buttons .btn-group .btn:not(.active):hover {
    background-color: var(--background-accent-light);
    color: var(--primary-color);
}

/* Search and Filter */
.search-filter-section {
    max-width: 250px;
}

.input-group-text {
    background-color: var(--background-secondary);
    border-color: var(--border-color);
    color: var(--text-secondary);
}

.form-control {
    border-color: var(--border-color);
    transition: all var(--transition-duration-base) var(--transition-easing-ease);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-contact-input-focus);
}

/* Contracts Container */
.contracts-container {
    min-height: 400px;
}

/* Card View (Default) */
.contracts-container.card-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--space-lg);
}

.contract-item {
    background: var(--background-secondary);
    border: var(--border-width-sm) solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    margin-bottom: 0;
    transition: all var(--transition-duration-base) var(--transition-easing-ease);
    cursor: pointer;
}

.contract-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

/* List View */
.contracts-container.list-view {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.contracts-container.list-view .contract-item {
    padding: var(--space-md);
    border-radius: var(--radius-md);
}

.contracts-container.list-view .contract-header {
    margin-bottom: var(--space-sm);
}

.contracts-container.list-view .contract-details {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-lg);
}

.contracts-container.list-view .contract-detail-item {
    min-width: 150px;
}

/* Compact View */
.contracts-container.compact-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--space-md);
}

.contracts-container.compact-view .contract-item {
    padding: var(--space-md);
    border-radius: var(--radius-md);
}

.contracts-container.compact-view .contract-title {
    font-size: var(--font-size-1_01-rem);
    margin-bottom: var(--space-xxs);
}

.contracts-container.compact-view .contract-details {
    grid-template-columns: 1fr;
    gap: var(--space-xs);
}

.contracts-container.compact-view .contract-detail-item {
    font-size: var(--font-size-0_9-rem);
}

/* Table View */
.contracts-container.table-view {
    display: block;
}

.contracts-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.contracts-table th,
.contracts-table td {
    padding: var(--space-md);
    text-align: left;
    border-bottom: var(--border-width-sm) solid var(--border-color);
}

.contracts-table th {
    background-color: var(--background-accent-light);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    font-size: var(--font-size-0_93-rem);
}

.contracts-table tbody tr {
    cursor: pointer;
    transition: all var(--transition-duration-base) var(--transition-easing-ease);
}

.contracts-table tbody tr:hover {
    background-color: var(--background-hover-light);
}

.contracts-table tbody tr:last-child td {
    border-bottom: none;
}

.contract-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-md);
}

.contract-title {
    font-size: var(--font-size-1_22-rem);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-xs);
}

.contract-type-badge {
    background: var(--primary-color);
    color: var(--text-white);
    padding: var(--space-xxs) var(--space-sm);
    border-radius: var(--radius-button-sm);
    font-size: var(--font-size-0_8-rem);
    font-weight: var(--font-weight-medium);
}

.contract-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-md);
}

.contract-detail-item {
    display: flex;
    align-items: center;
}

.contract-detail-label {
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    margin-right: var(--space-xs);
    min-width: 80px;
}

.contract-detail-value {
    color: var(--text-primary);
}

/* Status Badges */
.status-badge {
    padding: var(--space-xxs) var(--space-sm);
    border-radius: var(--radius-button-sm);
    font-size: var(--font-size-0_8-rem);
    font-weight: var(--font-weight-medium);
}

.status-active {
    background-color: var(--success-light);
    color: var(--success-main);
}

.status-pending {
    background-color: var(--warning-light);
    color: var(--warning-dark);
}

.status-expired {
    background-color: var(--error-light);
    color: var(--error-main);
}

/* Modal Styles */
.modal-content {
    border: none;
    border-radius: var(--radius-card);
    box-shadow: var(--shadow-xl);
}

.modal-header {
    background: var(--gradient-submit);
    color: var(--text-white);
    border-radius: var(--radius-card) var(--radius-card) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header-actions {
    display: flex;
    align-items: center;
}

.modal-title {
    font-weight: var(--font-weight-semibold);
}

.btn-close {
    filter: invert(1);
}

/* Step Progress Styles */
.step-progress-container {
    background-color: var(--background-secondary);
    padding: var(--space-lg) var(--space-xl);
    border-bottom: var(--border-width-sm) solid var(--border-color);
}

.step-progress {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 600px;
    margin: 0 auto;
}

.step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    flex: 1;
    max-width: 150px;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--border-color);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-0_93-rem);
    margin-bottom: var(--space-xs);
    transition: all var(--transition-duration-base) var(--transition-easing-ease);
}

.step-item.active .step-number {
    background-color: var(--primary-color);
    color: var(--text-white);
}

.step-item.completed .step-number {
    background-color: var(--success-main);
    color: var(--text-white);
}

.step-label {
    font-size: var(--font-size-0_9-rem);
    color: var(--text-secondary);
    text-align: center;
    font-weight: var(--font-weight-medium);
}

.step-item.active .step-label {
    color: var(--primary-color);
    font-weight: var(--font-weight-semibold);
}

.step-connector {
    height: 2px;
    background-color: var(--border-color);
    flex: 1;
    margin: 0 var(--space-sm);
    margin-bottom: 20px;
}

.step-item.completed + .step-connector {
    background-color: var(--success-main);
}

/* Step Content Styles */
.step-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.step-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-lg);
    padding-bottom: var(--space-md);
    border-bottom: var(--border-width-sm) solid var(--border-color);
}

.step-title {
    font-size: var(--font-size-1_22-rem);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.step-fields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-lg);
    min-height: 300px;
}

/* Custom Field Styles */
.custom-field-item {
    position: relative;
    background: var(--background-accent-light);
    border: var(--border-width-sm) solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--space-md);
    transition: all var(--transition-duration-base) var(--transition-easing-ease);
}

.custom-field-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.custom-field-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-sm);
}

.custom-field-label {
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    font-size: var(--font-size-0_93-rem);
}

.custom-field-actions {
    display: flex;
    gap: var(--space-xs);
}

.btn-field-action {
    padding: var(--space-xxs) var(--space-xs);
    font-size: var(--font-size-0_8-rem);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: var(--radius-sm);
    transition: all var(--transition-duration-base) var(--transition-easing-ease);
}

.btn-field-action:hover {
    background-color: var(--background-secondary);
    color: var(--text-primary);
}

.btn-field-action.delete:hover {
    background-color: var(--error-light);
    color: var(--error-main);
}

/* Form Styles */
.form-section {
    margin-bottom: var(--space-xl);
}

.form-section-title {
    font-size: var(--font-size-1_22-rem);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-md);
    padding-bottom: var(--space-xs);
    border-bottom: var(--border-width-sm) solid var(--border-color);
}

.form-label {
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--space-xs);
}

.form-control, .form-select {
    border-color: var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--space-sm);
    transition: all var(--transition-duration-base) var(--transition-easing-ease);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-contact-input-focus);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--background-overlay);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: var(--z-index-modal);
}

.loading-overlay.show {
    display: flex;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-section {
        text-align: center;
    }

    .page-title {
        font-size: var(--font-size-xxl);
    }

    .contract-type-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--space-sm);
    }

    .contract-details {
        grid-template-columns: 1fr;
        gap: var(--space-sm);
    }

    .search-filter-section {
        max-width: 100%;
        margin-top: var(--space-md);
    }

    /* Mobile view adjustments */
    .card-header .row {
        flex-direction: column;
        gap: var(--space-md);
    }

    .view-toggle-section {
        order: 2;
    }

    .view-mode-buttons {
        order: 3;
        margin-right: 0 !important;
        margin-bottom: var(--space-sm);
    }

    .view-mode-buttons .btn-group {
        width: 100%;
    }

    .view-mode-buttons .btn-group .btn {
        flex: 1;
        font-size: var(--font-size-0_75-rem);
        padding: var(--space-xs);
    }

    /* Force card view on mobile for better UX */
    .contracts-container.card-view,
    .contracts-container.compact-view {
        grid-template-columns: 1fr;
    }

    .contracts-container.list-view .contract-details {
        flex-direction: column;
        gap: var(--space-sm);
    }

    /* Table view on mobile - make it scrollable */
    .contracts-container.table-view {
        overflow-x: auto;
    }

    .contracts-table {
        min-width: 600px;
    }

    /* Step modal responsive */
    .step-progress {
        flex-direction: column;
        gap: var(--space-sm);
    }

    .step-item {
        flex-direction: row;
        max-width: 100%;
    }

    .step-number {
        margin-bottom: 0;
        margin-right: var(--space-sm);
    }

    .step-connector {
        display: none;
    }

    .step-fields {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }

    .modal-header-actions {
        flex-direction: column;
        gap: var(--space-xs);
    }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--space-xxl);
    color: var(--text-secondary);
}

.empty-state-icon {
    font-size: var(--font-size-4-rem);
    color: var(--border-color);
    margin-bottom: var(--space-md);
}

.empty-state-title {
    font-size: var(--font-size-1_22-rem);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-sm);
}

.empty-state-description {
    font-size: var(--font-size-base);
    margin-bottom: var(--space-lg);
}

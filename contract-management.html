<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contract Management - Billing Portal</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="Font.css">
    <link rel="stylesheet" href="Root.css">
    <link rel="stylesheet" href="contract-management.css">
</head>
<body>
    <!-- Header -->
    <header class="header-section">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="page-title">
                        <i class="bi bi-file-earmark-text me-2"></i>
                        Contract Management
                    </h1>
                    <p class="page-subtitle">Manage all your contracts efficiently</p>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-primary-custom" id="addContractBtn">
                        <i class="bi bi-plus-circle me-2"></i>
                        Add New Contract
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
            <!-- Contract Type Selection -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card contract-type-card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-collection me-2"></i>
                                Contract Types
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="contract-type-grid" id="contractTypeGrid">
                                <!-- Contract types will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contract List -->
            <div class="row">
                <div class="col-12">
                    <div class="card contracts-list-card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-list-ul me-2"></i>
                                        Contracts List
                                    </h5>
                                </div>
                                <div class="col-md-4">
                                    <!-- View Toggle Buttons -->
                                    <div class="view-toggle-section">
                                        <button class="btn btn-outline-secondary btn-sm me-1" id="advancedSearchBtn">
                                            <i class="bi bi-funnel me-1"></i>
                                            Advanced Search
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm" id="tableSettingsBtn">
                                            <i class="bi bi-gear me-1"></i>
                                            Table Settings
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center justify-content-end">
                                        <!-- View Mode Buttons -->
                                        <div class="view-mode-buttons me-3">
                                            <div class="btn-group" role="group" aria-label="View modes">
                                                <button type="button" class="btn btn-primary-custom btn-sm active" id="cardViewBtn" data-view="card">
                                                    <i class="bi bi-grid-3x3-gap me-1"></i>
                                                    Card
                                                </button>
                                                <button type="button" class="btn btn-outline-primary btn-sm" id="compactViewBtn" data-view="compact">
                                                    <i class="bi bi-grid me-1"></i>
                                                    Compact
                                                </button>
                                                <button type="button" class="btn btn-outline-primary btn-sm" id="listViewBtn" data-view="list">
                                                    <i class="bi bi-list me-1"></i>
                                                    List
                                                </button>
                                                <button type="button" class="btn btn-outline-primary btn-sm" id="tableViewBtn" data-view="table">
                                                    <i class="bi bi-table me-1"></i>
                                                    Table
                                                </button>
                                            </div>
                                        </div>
                                        <!-- Search -->
                                        <div class="search-filter-section">
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="bi bi-search"></i>
                                                </span>
                                                <input type="text" class="form-control" id="searchInput" placeholder="Search contracts...">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="contracts-container" id="contractsContainer">
                                <!-- Contracts will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Contract Details Modal -->
    <div class="modal fade" id="contractModal" tabindex="-1" aria-labelledby="contractModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="contractModalLabel">
                        <i class="bi bi-file-earmark-text me-2"></i>
                        Contract Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="contractForm">
                        <!-- Contract form will be loaded here -->
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary-custom" id="saveContractBtn">
                        <i class="bi bi-check-circle me-2"></i>
                        Save Contract
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="contract-management.js"></script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contract Management - Billing Portal</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="Font.css">
    <link rel="stylesheet" href="Root.css">
    <link rel="stylesheet" href="contract-management.css">
</head>
<body>
    <!-- Header -->
    <header class="header-section">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="page-title">
                        <i class="bi bi-file-earmark-text me-2"></i>
                        Contract Management
                    </h1>
                    <p class="page-subtitle">Manage all your contracts efficiently</p>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-primary-custom" id="addContractBtn">
                        <i class="bi bi-plus-circle me-2"></i>
                        Add New Contract
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
            <!-- Contract Type Selection -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card contract-type-card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-collection me-2"></i>
                                Contract Types
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="contract-type-grid" id="contractTypeGrid">
                                <!-- Contract types will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

                        <!-- Search and Controls Bar -->
                        <div class="search-controls-bar">
                            <div class="d-flex justify-content-between align-items-center">
                                <!-- Left side - Search and Select All -->
                                <div class="d-flex align-items-center gap-3">
                                    <div class="search-section">
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="bi bi-search"></i>
                                            </span>
                                            <input type="text" class="form-control" id="searchInput" placeholder="Search contracts...">
                                        </div>
                                    </div>
                                    <div class="select-all-section">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAllCheckbox">
                                            <label class="form-check-label" for="selectAllCheckbox">
                                                Select All
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Right side - View Mode Buttons and Actions -->
                                <div class="d-flex align-items-center gap-2">
                                    <!-- Bulk Actions (hidden by default) -->
                                    <div class="bulk-actions" id="bulkActions" style="display: none;">
                                        <button class="btn btn-outline-danger btn-sm" id="bulkDeleteBtn">
                                            <i class="bi bi-trash me-1"></i>
                                            Delete Selected
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm" id="bulkEditBtn">
                                            <i class="bi bi-pencil me-1"></i>
                                            Edit Selected
                                        </button>
                                    </div>

                                    <!-- Advanced Search Toggle -->
                                    <button class="btn btn-outline-info btn-sm" id="advancedSearchToggle">
                                        <i class="bi bi-funnel me-1"></i>
                                        Advanced Search
                                    </button>

                                    <!-- Table Settings -->
                                    <button class="btn btn-outline-info btn-sm" id="tableSettingsToggle">
                                        <i class="bi bi-gear me-1"></i>
                                        Table Settings
                                    </button>

                                    <!-- View Mode Buttons -->
                                    <div class="view-mode-buttons">
                                        <div class="btn-group" role="group" aria-label="View modes">
                                            <button type="button" class="btn btn-primary-custom btn-sm active" id="cardViewBtn" data-view="card" title="Card View">
                                                <i class="bi bi-grid-3x3-gap"></i>
                                                <span class="d-none d-md-inline ms-1">Card</span>
                                            </button>
                                            <button type="button" class="btn btn-outline-primary btn-sm" id="compactViewBtn" data-view="compact" title="Compact View">
                                                <i class="bi bi-grid"></i>
                                                <span class="d-none d-md-inline ms-1">Compact</span>
                                            </button>
                                            <button type="button" class="btn btn-outline-primary btn-sm" id="listViewBtn" data-view="list" title="List View">
                                                <i class="bi bi-list"></i>
                                                <span class="d-none d-md-inline ms-1">List</span>
                                            </button>
                                            <button type="button" class="btn btn-outline-primary btn-sm" id="tableViewBtn" data-view="table" title="Table View">
                                                <i class="bi bi-table"></i>
                                                <span class="d-none d-md-inline ms-1">Table</span>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Search Icon -->
                                    <button class="btn btn-outline-secondary btn-sm" id="searchToggleBtn" title="Search">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Search Panel (Hidden by default) -->
                        <div class="advanced-search-panel" id="advancedSearchPanel" style="display: none;">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">Contract Type</label>
                                    <select class="form-select form-select-sm" id="filterContractType">
                                        <option value="">All Types</option>
                                        <option value="SOW">SOW</option>
                                        <option value="MLA">MLA</option>
                                        <option value="MSA">MSA</option>
                                        <option value="GCA">GCA</option>
                                        <option value="NDA">NDA</option>
                                        <option value="DPA">DPA</option>
                                        <option value="SLA">SLA</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Status</label>
                                    <select class="form-select form-select-sm" id="filterStatus">
                                        <option value="">All Status</option>
                                        <option value="Active">Active</option>
                                        <option value="Pending">Pending</option>
                                        <option value="Expired">Expired</option>
                                        <option value="Terminated">Terminated</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Start Date From</label>
                                    <input type="date" class="form-control form-control-sm" id="filterStartDate">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">End Date To</label>
                                    <input type="date" class="form-control form-control-sm" id="filterEndDate">
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <button class="btn btn-primary-custom btn-sm me-2" id="applyFiltersBtn">
                                        <i class="bi bi-check me-1"></i>
                                        Apply Filters
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" id="clearFiltersBtn">
                                        <i class="bi bi-x me-1"></i>
                                        Clear Filters
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="contracts-container" id="contractsContainer">
                                <!-- Contracts will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Contract Details Modal -->
    <div class="modal fade" id="contractModal" tabindex="-1" aria-labelledby="contractModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="contractModalLabel">
                        <i class="bi bi-plus-circle me-2"></i>
                        Add Contract
                    </h5>
                    <div class="modal-header-actions">
                        <button type="button" class="btn btn-sm btn-outline-light me-2" id="saveContractBtn">
                            <i class="bi bi-floppy me-1"></i>
                            Save
                        </button>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                </div>

                <!-- Step Progress Indicator -->
                <div class="step-progress-container">
                    <div class="step-progress">
                        <div class="step-item active" data-step="1">
                            <div class="step-number">1</div>
                            <div class="step-label">Commercial</div>
                        </div>
                        <div class="step-connector"></div>
                        <div class="step-item" data-step="2">
                            <div class="step-number">2</div>
                            <div class="step-label">Delivery Related</div>
                        </div>
                        <div class="step-connector"></div>
                        <div class="step-item" data-step="3">
                            <div class="step-number">3</div>
                            <div class="step-label">Other</div>
                        </div>
                    </div>
                </div>

                <div class="modal-body">
                    <form id="contractForm">
                        <!-- Step 1: Commercial Information -->
                        <div class="step-content active" id="step1">
                            <div class="step-header">
                                <h6 class="step-title">Commercial Information</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="addFieldBtn1">
                                    <i class="bi bi-plus me-1"></i>
                                    Add Field
                                </button>
                            </div>
                            <div class="step-fields" id="step1Fields">
                                <!-- Fields will be loaded here -->
                            </div>
                        </div>

                        <!-- Step 2: Delivery Related -->
                        <div class="step-content" id="step2">
                            <div class="step-header">
                                <h6 class="step-title">Delivery Related</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="addFieldBtn2">
                                    <i class="bi bi-plus me-1"></i>
                                    Add Field
                                </button>
                            </div>
                            <div class="step-fields" id="step2Fields">
                                <!-- Fields will be loaded here -->
                            </div>
                        </div>

                        <!-- Step 3: Other -->
                        <div class="step-content" id="step3">
                            <div class="step-header">
                                <h6 class="step-title">Other</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="addFieldBtn3">
                                    <i class="bi bi-plus me-1"></i>
                                    Add Field
                                </button>
                            </div>
                            <div class="step-fields" id="step3Fields">
                                <!-- Fields will be loaded here -->
                            </div>
                        </div>
                    </form>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" id="prevStepBtn" disabled>
                        <i class="bi bi-chevron-left me-1"></i>
                        Previous
                    </button>
                    <button type="button" class="btn btn-primary-custom" id="nextStepBtn">
                        Next
                        <i class="bi bi-chevron-right ms-1"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>
                        Cancel
                    </button>
                    <button type="button" class="btn btn-success" id="saveCloseBtn" style="display: none;">
                        <i class="bi bi-check-circle me-1"></i>
                        Save & Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Field Modal -->
    <div class="modal fade" id="addFieldModal" tabindex="-1" aria-labelledby="addFieldModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addFieldModalLabel">
                        <i class="bi bi-plus-square me-2"></i>
                        Add Custom Field
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addFieldForm">
                        <div class="mb-3">
                            <label for="fieldLabel" class="form-label">Field Label *</label>
                            <input type="text" class="form-control" id="fieldLabel" required>
                        </div>
                        <div class="mb-3">
                            <label for="fieldType" class="form-label">Field Type *</label>
                            <select class="form-select" id="fieldType" required>
                                <option value="">Select field type</option>
                                <option value="text">Text Input</option>
                                <option value="email">Email</option>
                                <option value="tel">Phone</option>
                                <option value="date">Date</option>
                                <option value="number">Number</option>
                                <option value="select">Dropdown</option>
                                <option value="textarea">Text Area</option>
                                <option value="checkbox">Checkbox</option>
                            </select>
                        </div>
                        <div class="mb-3" id="optionsContainer" style="display: none;">
                            <label for="fieldOptions" class="form-label">Options (one per line)</label>
                            <textarea class="form-control" id="fieldOptions" rows="3" placeholder="Option 1&#10;Option 2&#10;Option 3"></textarea>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="fieldRequired">
                                <label class="form-check-label" for="fieldRequired">
                                    Required field
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary-custom" id="saveFieldBtn">
                        <i class="bi bi-check-circle me-1"></i>
                        Add Field
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="contract-management.js"></script>
</body>
</html>

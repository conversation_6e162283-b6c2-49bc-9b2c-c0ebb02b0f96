<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contract Management - Billing Portal</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="Font.css">
    <link rel="stylesheet" href="Root.css">
    <link rel="stylesheet" href="contract-management.css">
</head>
<body>
    <!-- Header -->
    <header class="header-section">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="page-title">
                        <i class="bi bi-file-earmark-text me-2"></i>
                        Contract Management
                    </h1>
                    <p class="page-subtitle">Manage all your contracts efficiently</p>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-primary-custom" id="addContractBtn">
                        <i class="bi bi-plus-circle me-2"></i>
                        Add New Contract
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid">
            <!-- Contract Type Selection -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card contract-type-card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-collection me-2"></i>
                                Contract Types
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="contract-type-grid" id="contractTypeGrid">
                                <!-- Contract types will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contract List -->
            <div class="row">
                <div class="col-12">
                    <div class="card contracts-list-card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <h5 class="card-title mb-0">
                                        <i class="bi bi-list-ul me-2"></i>
                                        Contracts List
                                    </h5>
                                </div>
                                <div class="col-md-4">
                                    <!-- View Toggle Buttons -->
                                    <div class="view-toggle-section">
                                        <button class="btn btn-outline-secondary btn-sm me-1" id="advancedSearchBtn">
                                            <i class="bi bi-funnel me-1"></i>
                                            Advanced Search
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm" id="tableSettingsBtn">
                                            <i class="bi bi-gear me-1"></i>
                                            Table Settings
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-center justify-content-end">
                                        <!-- View Mode Buttons -->
                                        <div class="view-mode-buttons me-3">
                                            <div class="btn-group" role="group" aria-label="View modes">
                                                <button type="button" class="btn btn-primary-custom btn-sm active" id="cardViewBtn" data-view="card">
                                                    <i class="bi bi-grid-3x3-gap me-1"></i>
                                                    Card
                                                </button>
                                                <button type="button" class="btn btn-outline-primary btn-sm" id="compactViewBtn" data-view="compact">
                                                    <i class="bi bi-grid me-1"></i>
                                                    Compact
                                                </button>
                                                <button type="button" class="btn btn-outline-primary btn-sm" id="listViewBtn" data-view="list">
                                                    <i class="bi bi-list me-1"></i>
                                                    List
                                                </button>
                                                <button type="button" class="btn btn-outline-primary btn-sm" id="tableViewBtn" data-view="table">
                                                    <i class="bi bi-table me-1"></i>
                                                    Table
                                                </button>
                                            </div>
                                        </div>
                                        <!-- Search -->
                                        <div class="search-filter-section">
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i class="bi bi-search"></i>
                                                </span>
                                                <input type="text" class="form-control" id="searchInput" placeholder="Search contracts...">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="contracts-container" id="contractsContainer">
                                <!-- Contracts will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Contract Details Modal -->
    <div class="modal fade" id="contractModal" tabindex="-1" aria-labelledby="contractModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="contractModalLabel">
                        <i class="bi bi-plus-circle me-2"></i>
                        Add Contract
                    </h5>
                    <div class="modal-header-actions">
                        <button type="button" class="btn btn-sm btn-outline-light me-2" id="saveContractBtn">
                            <i class="bi bi-floppy me-1"></i>
                            Save
                        </button>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                </div>

                <!-- Step Progress Indicator -->
                <div class="step-progress-container">
                    <div class="step-progress">
                        <div class="step-item active" data-step="1">
                            <div class="step-number">1</div>
                            <div class="step-label">Commercial</div>
                        </div>
                        <div class="step-connector"></div>
                        <div class="step-item" data-step="2">
                            <div class="step-number">2</div>
                            <div class="step-label">Delivery Related</div>
                        </div>
                        <div class="step-connector"></div>
                        <div class="step-item" data-step="3">
                            <div class="step-number">3</div>
                            <div class="step-label">Other</div>
                        </div>
                    </div>
                </div>

                <div class="modal-body">
                    <form id="contractForm">
                        <!-- Step 1: Commercial Information -->
                        <div class="step-content active" id="step1">
                            <div class="step-header">
                                <h6 class="step-title">Commercial Information</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="addFieldBtn1">
                                    <i class="bi bi-plus me-1"></i>
                                    Add Field
                                </button>
                            </div>
                            <div class="step-fields" id="step1Fields">
                                <!-- Fields will be loaded here -->
                            </div>
                        </div>

                        <!-- Step 2: Delivery Related -->
                        <div class="step-content" id="step2">
                            <div class="step-header">
                                <h6 class="step-title">Delivery Related</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="addFieldBtn2">
                                    <i class="bi bi-plus me-1"></i>
                                    Add Field
                                </button>
                            </div>
                            <div class="step-fields" id="step2Fields">
                                <!-- Fields will be loaded here -->
                            </div>
                        </div>

                        <!-- Step 3: Other -->
                        <div class="step-content" id="step3">
                            <div class="step-header">
                                <h6 class="step-title">Other</h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="addFieldBtn3">
                                    <i class="bi bi-plus me-1"></i>
                                    Add Field
                                </button>
                            </div>
                            <div class="step-fields" id="step3Fields">
                                <!-- Fields will be loaded here -->
                            </div>
                        </div>
                    </form>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" id="prevStepBtn" disabled>
                        <i class="bi bi-chevron-left me-1"></i>
                        Previous
                    </button>
                    <button type="button" class="btn btn-primary-custom" id="nextStepBtn">
                        Next
                        <i class="bi bi-chevron-right ms-1"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>
                        Cancel
                    </button>
                    <button type="button" class="btn btn-success" id="saveCloseBtn" style="display: none;">
                        <i class="bi bi-check-circle me-1"></i>
                        Save & Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Field Modal -->
    <div class="modal fade" id="addFieldModal" tabindex="-1" aria-labelledby="addFieldModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addFieldModalLabel">
                        <i class="bi bi-plus-square me-2"></i>
                        Add Custom Field
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addFieldForm">
                        <div class="mb-3">
                            <label for="fieldLabel" class="form-label">Field Label *</label>
                            <input type="text" class="form-control" id="fieldLabel" required>
                        </div>
                        <div class="mb-3">
                            <label for="fieldType" class="form-label">Field Type *</label>
                            <select class="form-select" id="fieldType" required>
                                <option value="">Select field type</option>
                                <option value="text">Text Input</option>
                                <option value="email">Email</option>
                                <option value="tel">Phone</option>
                                <option value="date">Date</option>
                                <option value="number">Number</option>
                                <option value="select">Dropdown</option>
                                <option value="textarea">Text Area</option>
                                <option value="checkbox">Checkbox</option>
                            </select>
                        </div>
                        <div class="mb-3" id="optionsContainer" style="display: none;">
                            <label for="fieldOptions" class="form-label">Options (one per line)</label>
                            <textarea class="form-control" id="fieldOptions" rows="3" placeholder="Option 1&#10;Option 2&#10;Option 3"></textarea>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="fieldRequired">
                                <label class="form-check-label" for="fieldRequired">
                                    Required field
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary-custom" id="saveFieldBtn">
                        <i class="bi bi-check-circle me-1"></i>
                        Add Field
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="contract-management.js"></script>
</body>
</html>

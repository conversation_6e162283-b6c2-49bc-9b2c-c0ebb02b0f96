{"formFields": [{"sectionTitle": "Customer Information", "fields": [{"name": "customerName", "label": "Customer Name", "type": "text", "required": true, "colSize": "col-md-6"}, {"name": "groupCompanyName", "label": "Group Company Name", "type": "text", "required": true, "colSize": "col-md-6"}]}, {"sectionTitle": "Contract Details", "fields": [{"name": "contractType", "label": "Contract Type", "type": "select", "required": true, "colSize": "col-md-4", "options": [{"value": "SOW", "label": "Statement of Work (SOW)"}, {"value": "MLA", "label": "Master License Agreement (MLA)"}, {"value": "MSA", "label": "Master Service Agreement (MSA)"}, {"value": "GCA", "label": "General Contract Agreement (GCA)"}, {"value": "NDA", "label": "Non-Disclosure Agreement (NDA)"}, {"value": "DPA", "label": "Data Processing Agreement (DPA)"}, {"value": "SLA", "label": "Service Level Agreement (SLA)"}]}, {"name": "contractSubtype", "label": "Contract Subtype", "type": "text", "required": false, "colSize": "col-md-4"}, {"name": "status", "label": "Status", "type": "select", "required": true, "colSize": "col-md-4", "options": [{"value": "Active", "label": "Active"}, {"value": "Pending", "label": "Pending"}, {"value": "Expired", "label": "Expired"}, {"value": "Terminated", "label": "Terminated"}]}]}, {"sectionTitle": "Project Information", "fields": [{"name": "projectCode", "label": "Project Code", "type": "text", "required": false, "colSize": "col-md-6"}, {"name": "sowName", "label": "SOW/Project Name", "type": "text", "required": false, "colSize": "col-md-6"}, {"name": "scopeOfWork", "label": "Scope of Work", "type": "textarea", "required": false, "colSize": "col-md-12"}]}, {"sectionTitle": "Contract Timeline", "fields": [{"name": "startDate", "label": "Start Date", "type": "date", "required": true, "colSize": "col-md-6"}, {"name": "endDate", "label": "End Date", "type": "date", "required": true, "colSize": "col-md-6"}]}, {"sectionTitle": "Customer Contract Signatory", "fields": [{"name": "customerSignatoryCompany", "label": "Company Name", "type": "text", "required": true, "colSize": "col-md-6"}, {"name": "customerSignatoryName", "label": "Name", "type": "text", "required": true, "colSize": "col-md-6"}, {"name": "customerSignatoryDesignation", "label": "Designation", "type": "text", "required": true, "colSize": "col-md-4"}, {"name": "customerSignatoryEmail", "label": "Email", "type": "email", "required": true, "colSize": "col-md-4"}, {"name": "customerSignatoryPhone", "label": "Phone", "type": "tel", "required": true, "colSize": "col-md-4"}]}, {"sectionTitle": "Customer Contact Person", "fields": [{"name": "customerContactName", "label": "Name", "type": "text", "required": true, "colSize": "col-md-6"}, {"name": "customerContactDesignation", "label": "Designation", "type": "text", "required": true, "colSize": "col-md-6"}, {"name": "customerContactEmail", "label": "Email", "type": "email", "required": true, "colSize": "col-md-6"}, {"name": "customerContactPhone", "label": "Phone", "type": "tel", "required": true, "colSize": "col-md-6"}]}, {"sectionTitle": "HCL Information", "fields": [{"name": "hclCompanyName", "label": "HCL Company Name", "type": "text", "required": true, "colSize": "col-md-6"}, {"name": "contractCovered", "label": "Contract Covered", "type": "textarea", "required": false, "colSize": "col-md-6"}]}, {"sectionTitle": "HCL Contract Signatory", "fields": [{"name": "hclSignatoryCompany", "label": "Company Name", "type": "text", "required": true, "colSize": "col-md-6"}, {"name": "hclSignatoryName", "label": "Name", "type": "text", "required": true, "colSize": "col-md-6"}, {"name": "hclSignatoryDesignation", "label": "Designation", "type": "text", "required": true, "colSize": "col-md-4"}, {"name": "hclSignatoryEmail", "label": "Email", "type": "email", "required": true, "colSize": "col-md-4"}, {"name": "hclSignatoryPhone", "label": "Phone", "type": "tel", "required": true, "colSize": "col-md-4"}]}, {"sectionTitle": "HCL Contact Person", "fields": [{"name": "hclContactName", "label": "Name", "type": "text", "required": true, "colSize": "col-md-6"}, {"name": "hclContactDesignation", "label": "Designation", "type": "text", "required": true, "colSize": "col-md-6"}, {"name": "hclContactEmail", "label": "Email", "type": "email", "required": true, "colSize": "col-md-6"}, {"name": "hclContactPhone", "label": "Phone", "type": "tel", "required": true, "colSize": "col-md-6"}]}]}